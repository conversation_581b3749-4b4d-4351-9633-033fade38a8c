import Header from "@/components/ui/Header";
import PreHeader from "@/components/ui/PreHeader";

const steps = [
  {
    selector: '.first-step',
    content: 'This is my first Step',
  },
  // ...
]

export default function UpcomingLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative overflow-hidden w-full h-full bg-gray-50">
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 flex-grow">
        {children}
      </main>
    </div>
  )
}