import PreHeader from "@/components/ui/PreHeader";
import Footer from "@/components/ui/Footer";

export default function ResetPasswordLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative overflow-hidden min-h-screen w-full bg-gray-50">
      <PreHeader />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 flex-grow">
        {children}
      </main>
      <Footer />
    </div>
  )
}