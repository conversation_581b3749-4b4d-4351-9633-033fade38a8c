import Footer from "@/components/ui/Footer";
import Header from "@/components/ui/Header";
import PreHeader from "@/components/ui/PreHeader";
import { TourProvider } from '@reactour/tour'

const steps = [
  {
    selector: '.first-step',
    content: 'This is my first Step',
  },
  // ...
]

export default function AboutLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative overflow-hidden min-h-screen w-full">
      <div className='w-full h-full bg-gray-50'>
        <PreHeader />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 flex-grow">
          {children}
        </main>
      </div>
    </div>
  )
}